"use client"
import { useLocale, useTranslations } from "next-intl";
import { routing } from "@/i18n/routing";

export default function LocaleSwitcher() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="flex gap-2">
      {routing.locales.map((lang) => (
        <button
          key={lang}
          onClick={() => {
            window.location.href = `/${lang}`;
          }}
        >
          {lang === locale ? t("hello") : lang}
        </button>
      ))}
    </div>
  );
}
