export type SlideType = {
  id: number;
  image: string;
  title: string;
  title_ar: string;
  title_en: string;

  sub_title: string;
  sub_title_ar: string;
  sub_title_en: string;
  type: "banner" | "offer";
};
export type ProductType = {
  id: number;
  name_ar: string;
  name_en: string;
  name: string;
  description: string;
  price: number;
  new_price: number;
  rate: number;
  prices: {
    id: number;
    user_type_name: string;
    price: number;
  }[];
  avarage_rate: number;
  is_favorite: boolean;
  reviews_count: string;
  image: string;
  discount: string;
  store: number;
};


export type Review = {
  id: number;
  name: string;
  position: string;
  review: string;
};

