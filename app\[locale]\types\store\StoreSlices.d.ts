import { Locale } from "next-intl";
import { ModalSliceType } from "./modals";
export type HydrationState = {
  hasHydrated: boolean;
};
export type HydrationActions = {
  setHasHydrated: () => void;
};
export type HydrationSliceType = HydrationState & HydrationActions;
// .................................................................

export type User = {
  email: string; // The email of the user
  mobile: string; // The mobile number of the user
  name: string; // The name of the user
  status: "active" | "inactive"; // The status of the user, restricted to specific values
  type: "مشترى" | "بائع" | "آخر"; // The type of the user, restricted to specific Arabic values
  verified: boolean; // Whether the user is verified
};
export type AuthState = {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  verification_email: string;
  account_type: number;
};

export type AuthActions = {
  setIsAuthenticated: () => void;
  setUserData: (token: string, user: User) => void;
  resetUserData: () => void;

  setVerificationEmail: (email: string) => void;
  resetVerificationEmail: () => void;

  setAccountType: (account_type: number) => void;
  resetAccountType: () => void;
};
export type AuthSliceType = AuthState & AuthActions;
// .................................................................

export type SettingsState = {
  isSideMenuOpen: boolean;
  language:Locale;
};
export type SettingsActions = {
  openSideMenu: (open: boolean) => void;
  changeLanguage: (lang: Locale) => void;
};
export type SettingsSliceType = SettingsState & SettingsActions;
// .................................................................
export type AddressState = { selected_address?: Address };
export type AddressActions = {
  setSelectedAddress: (add: Address) => void;
  resetSelectedAddress: () => void;
};

export type AddressSliceType = AddressState & AddressActions;

// .................................................................

export type CartState = {
  promo_code?: PromoCodeType;
};
export type CartActions = {
  setSelectedPromo: (code: PromoCodeType) => void;
  resetSelectedPromo: () => void;
};
export type CartSliceType = CartState & CartActions;

// ...............................................................

export type StoreType = HydrationSliceType &
  ModalSliceType &
  AuthSliceType &
  SettingsSliceType &
  AddressSliceType &
  CartSliceType;
