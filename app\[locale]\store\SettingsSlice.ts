import { SettingsSliceType, SettingsState } from "@/types/store/StoreSlices";
import cookies from "js-cookie";

export const InitState: SettingsState = {
  isSideMenuOpen: false,
  language: cookies.get("NEXT_LOCALE") || "ar",
};

export const SettingsSlice = ({ set, get }: any): SettingsSliceType => ({
  ...InitState,
  changeLanguage: (lang) => {
    // cookies.set("NEXT_LOCALE", lang);
    set({ language: lang });
  },
  openSideMenu: (open: boolean) => set({ isSideMenuOpen: open }),
});
