import { Address } from "@/types/addressApi";
import { AddressState } from "@/types/store/StoreSlices";
import { produce } from "immer";

export const InitState: AddressState = {
  selected_address: undefined,
};

export const AddressSlice = ({ set, get }: any) => {
  return {
    ...InitState,

    setSelectedAddress: (add: Address) => {
      set((state: AddressState) =>
        produce(state, (draft) => {
          draft.selected_address = add;
        })
      );
    },

    resetSelectedAddress: () => {
      set((state: AddressState) =>
        produce(state, (draft) => {
          draft.selected_address = undefined;
        })
      );
    },
  };
};

// export const AddressSlice = ({ set, get }: any) => ({
//   ...InitState,
//   setSelectedAddress: (add: Address) => {
//     set((state: AddressState) =>
//       produce(state, (draft) => {
//         draft.selected_address = add;
//       })
//     );
//   },

//   resetSelectedAddress: () => {
//     set((state: AddressState) =>
//       produce(state, (draft) => {
//         draft.selected_address = undefined;
//       })
//     );
//   },
// });
