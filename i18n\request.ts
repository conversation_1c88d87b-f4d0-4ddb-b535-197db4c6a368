import { getRequestConfig } from "next-intl/server";
import { hasLocale } from "next-intl";
import { routing } from "./routing";

export default getRequestConfig(async ({ requestLocale }) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
    onError: (error) => {
      // Log the error but don't throw
      console.warn("Translation error:", error.message);
    },
    getMessageFallback: ({ namespace, key, error }) => {
      // Return the key as fallback instead of throwing
      return `${namespace}.${key}`;
    },
  };
});
