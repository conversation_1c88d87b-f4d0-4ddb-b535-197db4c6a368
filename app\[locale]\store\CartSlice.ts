import { PromoCodeType } from "@/types/cartApi";
import { CartState } from "@/types/store/StoreSlices";
import { produce } from "immer";

export const InitState: CartState = {
  promo_code: undefined,
};

export const CartSlice = ({ set, get }: any) => ({
  ...InitState,
  setSelectedPromo: (code: PromoCodeType) => {
    set((state: CartState) =>
      produce(state, (draft) => {
        draft.promo_code = code;
      })
    );
  },

  resetSelectedPromo: () => {
    set((state: CartState) =>
      produce(state, (draft) => {
        draft.promo_code = undefined;
      })
    );
  },
});
