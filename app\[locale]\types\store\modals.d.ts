import { Address } from "@/types/address";
import { AddressCardType } from "../profileApi";

export type ModalsState = {
  modals: {
    confirmation_modal: {
      open: boolean;
      data: {
        type: "success" | "failure" | "warning";
        title: string;
        message: string;
        message2?: string;
        confirm_btn?: string;
        cancel_btn?: string;
        onConfirmClicked?: () => void;
        onCancelClicked?: () => void;
      };
    };
    address_modal: {
      open: boolean;
      data: {
        address?: AddressCardType;
      };
    };
    delete_account_modal: {
      open: boolean;
      data?: any;
    };
    otp_modal: {
      open: boolean;
      data: { email: string };
    };
    review_product_modal: {
      open: boolean;
      data: {
        product_id?: number;
      };
    };
    product_gallery_modal: {
      open: boolean;
      data: {
        images: Array;
        video_url: string;
      };
    };
    select_address_modal: {
      open: boolean;
      data: { addresses: Address[] };
    };
    edit_address_modal: {
      open: boolean;
      data: { address?: Address };
    };
  };
};

export type ModalData<T extends ModalsNames> =
  T extends keyof ModalsState["modals"]
    ? ModalsState["modals"][T]["data"]
    : never;

export type OpenModalArgs<T extends ModalsNames> = {
  name: T;
  isOpen: boolean;
  data?: ModalData<T>;
};

export type ModalsActions = {
  openModal: <T extends ModalsNames>(args: OpenModalArgs<T>) => void;
  resetAllModals: () => void;
};

export type ModalSliceType = ModalsState & ModalsActions;

export type ModalsNames =
  | "confirmation_modal"
  | "address_modal"
  | "delete_account_modal"
  | "otp_modal"
  | "review_product_modal"
  | "product_gallery_modal"
  | "select_address_modal"
  | "edit_address_modal";
