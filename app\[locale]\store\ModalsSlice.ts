import { ModalsNames, ModalsState, OpenModalArgs } from "@/types/store/modals";
import { produce } from "immer";
export const InitState: ModalsState = {
  modals: {
    confirmation_modal: {
      open: false,
      data: {
        type: "success",
        title: "",
        message: "",
        confirm_btn: undefined,
        cancel_btn: undefined,
        onConfirmClicked: undefined,
        onCancelClicked: undefined,
      },
    },
    address_modal: {
      open: false,
      data: {
        address: undefined,
      },
    },
    delete_account_modal: {
      open: false,
      data: undefined,
    },
    otp_modal: {
      open: false,
      data: { email: "" },
    },
    review_product_modal: {
      open: false,
      data: {
        product_id: undefined,
      },
    },
    product_gallery_modal: {
      open: false,
      data: {
        images: [],
        video_url: "",
      },
    },
    select_address_modal: {
      open: false,
      data: {
        addresses: [],
      },
    },
    edit_address_modal: {
      open: false,
      data: {
        address: undefined,
      },
    },
  },
};

export const ModalsSlice = ({ set, get }: any) => ({
  ...InitState,
  openModal: <T extends ModalsNames>({
    name,
    isOpen,
    data,
  }: OpenModalArgs<T>) => {
    switch (name) {
      case "confirmation_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.confirmation_modal.open = isOpen;
            draft.modals.confirmation_modal.data =
              data as typeof draft.modals.confirmation_modal.data;
          })
        );
        break;

      case "delete_account_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.delete_account_modal.open = isOpen;
            draft.modals.delete_account_modal.data =
              data as typeof draft.modals.delete_account_modal.data;
          })
        );
        break;

      case "address_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.address_modal.open = isOpen;
            draft.modals.address_modal.data =
              data as typeof draft.modals.address_modal.data;
          })
        );
        break;

      case "otp_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.otp_modal.open = isOpen;
            draft.modals.otp_modal.data =
              data as typeof draft.modals.otp_modal.data;
          })
        );
        break;

      case "review_product_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.review_product_modal.open = isOpen;
            draft.modals.review_product_modal.data =
              data as typeof draft.modals.review_product_modal.data;
          })
        );
        break;

      case "product_gallery_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.product_gallery_modal.open = isOpen;
            draft.modals.product_gallery_modal.data =
              data as typeof draft.modals.product_gallery_modal.data;
          })
        );
        break;

      case "select_address_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.select_address_modal.open = isOpen;
            draft.modals.select_address_modal.data =
              data as typeof draft.modals.select_address_modal.data;
          })
        );
        break;
      case "edit_address_modal":
        set((state: ModalsState) =>
          produce(state, (draft) => {
            draft.modals.edit_address_modal.open = isOpen;
            draft.modals.edit_address_modal.data =
              data as typeof draft.modals.edit_address_modal.data;
          })
        );
        break;

      default:
        break;
    }
  },
  resetAllModals: () => {
    set(() => InitState);
  },
});
