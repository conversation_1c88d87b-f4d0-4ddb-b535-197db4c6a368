import { setCookieByServerAction, deleteCookieByServerAction } from "@/actions";
import { AuthState, User } from "@/types/store/StoreSlices";

export const InitState: AuthState = {
  isAuthenticated: false,
  token: null,
  user: null,
  verification_email: "",
  account_type: -1,
};

export const AuthSlice = ({ set, get }: any) => ({
  ...InitState,

  setIsAuthenticated: () => {
    setCookieByServerAction("isAuthenticated", "true");
    set({ isAuthenticated: true });
  },

  setUserData: (token: string, user: User) => {
    if (user.verified === true) {
      setCookieByServerAction("token", token);
      // Cookies.set("token", token);

      setCookieByServerAction(
        "isAuthenticated",
        token && user.verified ? "true" : ""
      );
      // Cookies.set("token", token);

      setCookieByServerAction("account_type", user.type);
      // Cookies.set("account_type", user.type);

      set({ token, user, isAuthenticated: token && user.verified });
    }
  },

  resetUserData: () => {
    deleteCookieByServerAction("token");
    deleteCookieByServerAction("isAuthenticated");
    deleteCookieByServerAction("account_type");

    set(InitState);
    // set({ account_type: -1 });
    // set({ verification_email: "" });
    // set({ token: null, user: null, isAuthenticated: false });
  },

  // email displayed in otp
  setVerificationEmail: (email: string) => {
    set({ verification_email: email });
  },

  resetVerificationEmail: () => {
    set({ verification_email: "" });
  },
  // email displayed in otp
  setAccountType: (account_type: number) => {
    set({ account_type });
  },

  resetAccountType: () => {
    set({ account_type: -1 });
  },
});
