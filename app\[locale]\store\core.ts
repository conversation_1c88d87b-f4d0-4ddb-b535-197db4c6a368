import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

import { StoreType } from "@/types/store/StoreSlices";
import { HydrationSlice } from "./HydrationSlice";
import { ModalsSlice } from "./ModalsSlice";
import { AuthSlice } from "./AuthSlice";
import { SettingsSlice } from "./SettingsSlice";
import { AddressSlice } from "./AddressSlice";
import { CartSlice } from "./CartSlice";

export const useCore = create<StoreType>()(
  devtools(
    persist(
      (set, ...a) => ({
        ...ModalsSlice({ set, ...a }),
        ...HydrationSlice({ set, ...a }),
        ...AuthSlice({ set, ...a }),
        ...SettingsSlice({ set, ...a }),
        ...AddressSlice({ set, ...a }),
        ...CartSlice({ set, ...a }),
      }),
      {
        name: "<PERSON><PERSON><PERSON>",
        version: 0,
        // onRehydrateStorage: () => (state) => {
        //   state?.setHasHydrated();
        // },

        // persist
        partialize: (state) => ({
          verification_email: state.verification_email,
          account_type: state.account_type,
          token: state.token,
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    )
  )
);
